# Flow Canvas Components

This directory contains the React Flow canvas implementation with custom node components for AI-powered video interaction.

## Components Overview

### FlowCanvas (`flow-canvas.tsx`)
The main canvas component that renders the React Flow interface with:
- Background grid
- Controls (zoom, fit view, etc.)
- MiniMap for navigation
- Custom node types registration
- Node and edge state management

### FlowToolbar (`flow-toolbar.tsx`)
A floating toolbar that allows users to add different types of nodes:
- YouTube Video nodes
- Voice Recorder nodes
- Image nodes
- Chat Interface nodes

## Node Components

### YouTubeNode (`nodes/youtube-node.tsx`)
**Features:**
- URL input for YouTube videos
- Video thumbnail display
- Play button overlay
- External link to open in YouTube
- Animated loading states
- Error handling for invalid thumbnails

**Animations:**
- Smooth fade-in when video loads
- Hover effects on buttons
- Scale animations for interactive elements

### VoiceRecorderNode (`nodes/voice-recorder-node.tsx`)
**Features:**
- Record audio using Web Audio API
- Play/pause recorded audio
- Upload existing audio files
- Visual recording indicator
- Duration display
- Delete recordings

**Animations:**
- Pulsing record button during recording
- Smooth transitions between recording/playback states
- Animated timer display
- Button hover/tap effects

### ImageNode (`nodes/image-node.tsx`)
**Features:**
- Drag & drop image upload
- File browser upload
- Image preview with controls
- Zoom, download, and delete actions
- File size and name display
- Multiple image format support

**Animations:**
- Drag state animations
- Image load transitions
- Button hover effects
- Smooth state transitions

### ChatNode (`nodes/chat-node.tsx`)
**Features:**
- ChatGPT-like interface
- AI model selection dropdown
- Message history with timestamps
- Typing indicators
- Auto-scroll to latest messages
- User and assistant message differentiation

**Animations:**
- Message bubble animations
- Typing indicator with bouncing dots
- Smooth scroll animations
- Button interactions

## Technologies Used

- **React Flow (@xyflow/react)**: Canvas and node management
- **Framer Motion**: Smooth animations and transitions
- **shadcn/ui**: Consistent UI components
- **React Dropzone**: File upload functionality
- **Web Audio API**: Voice recording capabilities
- **Tailwind CSS**: Styling and responsive design

## Usage

1. Navigate to a board page (`/dashboard/[boardId]`)
2. Use the "Add Node" dropdown to create different types of nodes
3. Connect nodes by dragging from handles
4. Interact with each node's specific functionality
5. Nodes automatically save their state

## Customization

Each node component can be extended with additional features:
- Add more AI models to the chat dropdown
- Implement YouTube API for real video metadata
- Add more image processing features
- Extend voice recording with transcription
- Add node-to-node data flow

## Animation Guidelines

All animations follow these principles:
- Smooth 60fps transitions
- Meaningful motion that guides user attention
- Consistent timing and easing
- Reduced motion respect for accessibility
- Performance-optimized with proper cleanup
