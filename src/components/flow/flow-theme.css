@import "tailwindcss";

/* React Flow Dark Theme Overrides */

/* Controls styling */
.react-flow__controls {
  @apply bg-background shadow-md !important;
}

.react-flow__controls-button {
  @apply bg-background text-foreground transition-all duration-200 ease-in-out !important;
}

.react-flow__controls-button:hover {
  @apply bg-accent text-accent-foreground border-accent !important;
}

.react-flow__controls-button:disabled {
  @apply bg-muted text-muted-foreground border-border opacity-50 !important;
}

/* MiniMap styling */
.react-flow__minimap {
  @apply bg-background border border-border rounded-[calc(theme(borderRadius.DEFAULT)-2px)] shadow-md !important;
}

.react-flow__minimap-mask {
  @apply bg-background/80 !important;
}

.react-flow__minimap-node {
  @apply fill-muted stroke-border stroke-[1px] !important;
}

/* Background styling */
.react-flow__background {
  @apply bg-background !important;
}

/* Background dots styling */
.react-flow__background-pattern {
  @apply fill-border opacity-40 !important;
}

/* Dark theme background dots */
.dark .react-flow__background-pattern {
  @apply fill-border opacity-60 !important;
}

/* Edge styling - Light theme */
.react-flow__edge-path {
  @apply stroke-[#6b7280] stroke-[2px] opacity-80 !important;
}

.react-flow__edge.selected .react-flow__edge-path {
  @apply stroke-primary stroke-[3px] opacity-100 !important;
}

.react-flow__edge:hover .react-flow__edge-path {
  @apply stroke-primary opacity-100 !important;
}

.react-flow__connectionline {
  @apply stroke-primary stroke-[2px] stroke-[5,5] opacity-100 !important;
}

/* Edge styling - Dark theme */
.dark .react-flow__edge-path {
  @apply stroke-[#9ca3af] stroke-[2px] opacity-90 !important;
}

.dark .react-flow__edge.selected .react-flow__edge-path {
  @apply stroke-primary stroke-[3px] opacity-100 !important;
}

.dark .react-flow__edge:hover .react-flow__edge-path {
  @apply stroke-primary opacity-100 !important;
}

.dark .react-flow__connectionline {
  @apply stroke-primary stroke-[2px] stroke-[5,5] opacity-100 !important;
}

/* Edge labels */
.react-flow__edge-text {
  @apply fill-foreground text-xs !important;
}

.react-flow__edge-textbg {
  @apply fill-background stroke-border stroke-[1px] !important;
}

/* Handle styling */
.react-flow__handle {
  @apply bg-background border-2 border-primary w-3 h-3 rounded-full opacity-0 transition-all duration-200 ease-in-out z-10 !important;
}

.react-flow__handle:hover {
  @apply bg-primary scale-120 opacity-100 shadow-[0_0_0_3px_rgba(var(--primary),0.3)] !important;
}

.react-flow__handle-connecting {
  @apply bg-primary scale-130 opacity-100 shadow-[0_0_0_4px_rgba(var(--primary),0.4)] !important;
}

/* Handle visibility on node hover - Show all handles */
.react-flow__node:hover .react-flow__handle {
  @apply opacity-100 bg-background border-primary !important;
}

/* Handle positioning and visibility */
.react-flow__handle-top {
  @apply -top-1.5 !important;
}

.react-flow__handle-bottom {
  @apply -bottom-1.5 !important;
}

.react-flow__handle-left {
  @apply -left-1.5 !important;
}

.react-flow__handle-right {
  @apply -right-1.5 !important;
}

/* Handle spacing for multiple handles on same side */
.react-flow__handle[id*="source"] {
  @apply translate-x-[3px] !important;
}

.react-flow__handle[id*="target"] {
  @apply -translate-x-[3px] !important;
}

/* Specific positioning for top/bottom handles */
.react-flow__handle-top[id*="source"] {
  @apply -translate-y-[3px] translate-x-[3px] !important;
}

.react-flow__handle-top[id*="target"] {
  @apply -translate-y-[3px] -translate-x-[3px] !important;
}

.react-flow__handle-bottom[id*="source"] {
  @apply translate-y-[3px] translate-x-[3px] !important;
}

.react-flow__handle-bottom[id*="target"] {
  @apply translate-y-[3px] -translate-x-[3px] !important;
}

/* Enhanced handle visibility for better UX */
.react-flow__node.selected .react-flow__handle {
  @apply opacity-100 !important;
}

/* Handle styling for dark theme */
.dark .react-flow__handle {
  @apply bg-background border-primary !important;
}

.dark .react-flow__handle:hover {
  @apply bg-primary shadow-[0_0_0_3px_rgba(var(--primary),0.3)] !important;
}

/* Selection box styling */
.react-flow__selection {
  @apply bg-primary/10 border-2 border-primary rounded !important;
}

/* Node selection styling */
.react-flow__node.selected {
  @apply shadow-[0_0_0_2px_var(--primary)] rounded-[calc(theme(borderRadius.DEFAULT)+2px)] !important;
}

/* Multi-selection styling */
.react-flow__node.selected:not(:only-child) {
  @apply shadow-[0_0_0_2px_var(--primary),0_0_0_4px_rgba(var(--primary),0.2)] !important;
}

/* Pane cursor styling for space key */
.react-flow__pane.dragging {
  @apply cursor-grabbing !important;
}

/* Attribution styling */
.react-flow__attribution {
  @apply bg-background text-muted-foreground border border-border rounded-[calc(theme(borderRadius.DEFAULT)-2px)] text-[10px] p-1 !important;
}

.react-flow__attribution a {
  @apply text-primary no-underline !important;
}

.react-flow__attribution a:hover {
  @apply underline !important;
}

/* Panel styling for any additional panels */
.react-flow__panel {
  @apply bg-background border border-border rounded-[calc(theme(borderRadius.DEFAULT)-2px)] shadow-md !important;
}

/* Viewport styling */
.react-flow__viewport {
  @apply bg-background !important;
}

/* Pane styling */
.react-flow__pane {
  @apply cursor-default !important;
}

.react-flow__pane.selection {
  @apply cursor-crosshair !important;
}

/* Transform styling */
.react-flow__transform {
  @apply bg-background !important;
}
