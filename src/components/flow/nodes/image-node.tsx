'use client';

import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Handle, type NodeProps, Position } from '@xyflow/react';
import { AnimatePresence, motion } from 'framer-motion';
import {
  DownloadIcon,
  ImageIcon,
  TrashIcon,
  UploadIcon,
  ZoomInIcon,
} from 'lucide-react';
import { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';

interface ImageData {
  imageUrl?: string;
  imageName?: string;
  imageSize?: number;
  label?: string;
}

export function ImageNode({ data }: NodeProps) {
  const nodeData = data as ImageData;
  const [imageUrl, setImageUrl] = useState<string | null>(
    nodeData.imageUrl || null
  );
  const [imageName, setImageName] = useState<string>(nodeData.imageName || '');
  const [imageSize, setImageSize] = useState<number>(nodeData.imageSize || 0);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (file?.type.startsWith('image/')) {
      const url = URL.createObjectURL(file);
      setImageUrl(url);
      setImageName(file.name);
      setImageSize(file.size);
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.svg'],
    },
    maxFiles: 1,
    maxSize: 10 * 1024 * 1024, // 10MB
  });

  const handleFileUpload = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (file?.type.startsWith('image/')) {
        const url = URL.createObjectURL(file);
        setImageUrl(url);
        setImageName(file.name);
        setImageSize(file.size);
      }
    },
    []
  );

  const deleteImage = useCallback(() => {
    if (imageUrl) {
      URL.revokeObjectURL(imageUrl);
    }
    setImageUrl(null);
    setImageName('');
    setImageSize(0);
  }, [imageUrl]);

  const downloadImage = useCallback(() => {
    if (imageUrl && imageName) {
      const link = document.createElement('a');
      link.href = imageUrl;
      link.download = imageName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }, [imageUrl, imageName]);

  const openImageInNewTab = useCallback(() => {
    if (imageUrl) {
      window.open(imageUrl, '_blank');
    }
  }, [imageUrl]);

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return (
      Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    );
  };

  return (
    <Card className="w-80 min-h-[250px]">
      {/* Connection handles on all 4 sides */}
      <Handle type="target" position={Position.Top} id="top-target" />
      <Handle type="source" position={Position.Top} id="top-source" />
      <Handle type="target" position={Position.Left} id="left-target" />
      <Handle type="source" position={Position.Left} id="left-source" />
      <Handle type="target" position={Position.Right} id="right-target" />
      <Handle type="source" position={Position.Right} id="right-source" />
      <Handle type="target" position={Position.Bottom} id="bottom-target" />
      <Handle type="source" position={Position.Bottom} id="bottom-source" />

      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-sm">
          <ImageIcon className="h-4 w-4" />
          Image
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-3">
        <AnimatePresence mode="wait">
          {!imageUrl ? (
            <motion.div
              key="upload"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-3"
            >
              <motion.div
                {...getRootProps()}
                className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
                  isDragActive
                    ? 'border-primary bg-primary/5'
                    : 'border-muted-foreground/25 hover:border-primary/50'
                }`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                animate={isDragActive ? { scale: 1.05 } : { scale: 1 }}
              >
                <input {...getInputProps()} />
                <motion.div
                  animate={isDragActive ? { y: [-2, 2, -2] } : {}}
                  transition={{
                    repeat: isDragActive ? Number.POSITIVE_INFINITY : 0,
                    duration: 1,
                  }}
                >
                  <ImageIcon className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                </motion.div>
                <p className="text-sm text-muted-foreground">
                  {isDragActive
                    ? 'Drop the image here...'
                    : 'Drag & drop an image here, or click to select'}
                </p>
              </motion.div>

              <div className="text-center">
                <label htmlFor="image-upload" className="cursor-pointer">
                  <Button variant="outline" size="sm" asChild>
                    <span>
                      <UploadIcon className="h-4 w-4 mr-2" />
                      Upload Image
                    </span>
                  </Button>
                </label>
                <input
                  id="image-upload"
                  type="file"
                  accept="image/*"
                  onChange={handleFileUpload}
                  className="hidden"
                />
              </div>
            </motion.div>
          ) : (
            <motion.div
              key="display"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-3"
            >
              <div className="relative">
                <motion.img
                  src={imageUrl}
                  alt={imageName}
                  className="w-full h-40 object-cover rounded"
                  initial={{ scale: 0.8, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ duration: 0.3 }}
                />
                <motion.div
                  className="absolute top-2 right-2 flex gap-1"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.2 }}
                >
                  <motion.div
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <Button
                      size="sm"
                      variant="secondary"
                      onClick={openImageInNewTab}
                      className="h-6 w-6 p-0"
                    >
                      <ZoomInIcon className="h-3 w-3" />
                    </Button>
                  </motion.div>
                  <motion.div
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <Button
                      size="sm"
                      variant="secondary"
                      onClick={downloadImage}
                      className="h-6 w-6 p-0"
                    >
                      <DownloadIcon className="h-3 w-3" />
                    </Button>
                  </motion.div>
                  <motion.div
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={deleteImage}
                      className="h-6 w-6 p-0"
                    >
                      <TrashIcon className="h-3 w-3" />
                    </Button>
                  </motion.div>
                </motion.div>
              </div>

              <motion.div
                className="space-y-1"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                <div className="text-xs font-medium truncate" title={imageName}>
                  {imageName}
                </div>
                <div className="text-xs text-muted-foreground">
                  {formatFileSize(imageSize)}
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </CardContent>
    </Card>
  );
}
