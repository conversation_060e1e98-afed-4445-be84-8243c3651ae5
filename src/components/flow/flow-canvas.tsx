'use client';

import {
  Background,
  BackgroundVariant,
  type Connection,
  Controls,
  type Edge,
  MiniMap,
  type Node,
  type NodeTypes,
  PanOnScrollMode,
  ReactFlow,
  SelectionMode,
  addEdge,
  useEdgesState,
  useNodesState,
} from '@xyflow/react';
import { useCallback, useEffect, useState } from 'react';

import '@xyflow/react/dist/style.css';
import './flow-theme.css';

import { FlowToolbar } from './flow-toolbar';
import { ChatNode } from './nodes/chat-node';
import { ImageNode } from './nodes/image-node';
import { VoiceRecorderNode } from './nodes/voice-recorder-node';
import { YouTubeNode } from './nodes/youtube-node';

const nodeTypes = {
  youtube: YouTubeNode,
  voiceRecorder: VoiceRecorderNode,
  image: ImageNode,
  chat: ChatNode,
} satisfies NodeTypes;

const initialNodes: Node[] = [
  {
    id: '1',
    type: 'chat',
    position: { x: 250, y: 250 },
    data: { label: 'Chat Interface' },
  },
];

const initialEdges: Edge[] = [];

interface FlowCanvasProps {
  boardId: string;
}

export function FlowCanvas({ boardId: _boardId }: FlowCanvasProps) {
  // TODO: Use _boardId for saving/loading board state
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [isSpacePressed, setIsSpacePressed] = useState(false);

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  // Handle spacebar for panning
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.code === 'Space' && !event.repeat) {
        event.preventDefault();
        setIsSpacePressed(true);
      }
    };

    const handleKeyUp = (event: KeyboardEvent) => {
      if (event.code === 'Space') {
        event.preventDefault();
        setIsSpacePressed(false);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, []);

  const addNode = useCallback(
    (type: string) => {
      // Only create nodes for types that exist in nodeTypes
      const validTypes = Object.keys(nodeTypes);
      if (!validTypes.includes(type)) {
        console.warn(`Node type "${type}" is not implemented yet`);
        return;
      }

      const newNode: Node = {
        id: `${Date.now()}`,
        type,
        position: {
          x: Math.random() * 400,
          y: Math.random() * 400,
        },
        data: { label: `${type} node` },
      };
      setNodes((nds) => [...nds, newNode]);
    },
    [setNodes]
  );

  return (
    <div className="w-full h-full relative">
      <FlowToolbar onAddNode={addNode} />
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        nodeTypes={nodeTypes}
        fitView
        nodesDraggable={true}
        nodesConnectable={true}
        elementsSelectable={true}
        selectionMode={SelectionMode.Partial}
        multiSelectionKeyCode={['Meta', 'Ctrl']}
        deleteKeyCode={['Backspace', 'Delete']}
        panOnScroll={true}
        panOnScrollMode={PanOnScrollMode.Free}
        panOnDrag={isSpacePressed}
        zoomOnScroll={false}
        zoomOnDoubleClick={false}
        preventScrolling={false}
      >
        <Background
          variant={BackgroundVariant.Dots}
          color="var(--border)"
          gap={20}
          size={1}
        />
        <Controls
          className="!bg-accent p-1 [&>button]:!bg-accent [&>button]:!text-foreground [&>button:hover]:!bg-background"
          showZoom={true}
          showFitView={true}
          showInteractive={false}
        />
        <MiniMap
          className="!bg-background !border-border"
          nodeColor={(node) => {
            switch (node.type) {
              case 'youtube':
                return 'var(--destructive)';
              case 'voiceRecorder':
                return 'var(--primary)';
              case 'image':
                return 'var(--secondary)';
              case 'chat':
                return 'var(--accent)';
              default:
                return 'var(--muted)';
            }
          }}
          maskColor="rgba(var(--background), 0.8)"
        />
      </ReactFlow>
    </div>
  );
}
